<template>
  <div
    ref="drawerRef"
    class="unitconversion absolute z-[99] ml-2 w-[calc(100%-40px)] transition-all duration-300"
    :class="
      drawerVisible
        ? 'pointer-events-auto opacity-100'
        : 'pointer-events-none opacity-0'
    "
    :style="{ bottom: isOpen ? '0px' : `-${drawerHeight - 38}px` }"
  >
    <ElCard class="w-full">
      <template #header>
        <div class="flex justify-between text-[14px]">
          <div>单位换算</div>
          <IconifyIcon
            @click="changeOpenStatus"
            class="icon cursor-pointer"
            :icon="isOpen ? 'codicon:fold-down' : 'codicon:fold-up'"
          />
          <IconifyIcon
            @click="closeClick"
            class="icon cursor-pointer"
            icon="ep:close-bold"
          />
        </div>
      </template>
      <div>
        <div class="mb-2 flex items-center">
          <ElButton
            size="small"
            type="primary"
            @click="addUnit"
            v-if="props.options.editable"
          >
            新增单位换算
          </ElButton>

          <div class="ml-10 flex text-[14px]">
            字典标准单位：
            <div class="text-orange-500">{{ _options.defaultUnit }}</div>
          </div>
        </div>
        <VxeGrid ref="tableRef" v-bind="tableOptions" v-on="tableEvents">
          <template #unit="{ row }">
            <vxe-input v-model="row.unit" placeholder="请输入单位" />
          </template>
          <template #type="{ row }">
            <div v-if="!row.isOperation">字典</div>
            <div v-else>合同</div>
          </template>
          <template #factor="{ row }">
            <vxe-number-input
              size="small"
              type="float"
              digits="5"
              :min="0"
              :auto-fill="false"
              v-model="row.factor"
              controls-position="right"
              placeholder="请输入换算系数"
            />
          </template>
        </VxeGrid>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { ElButton, ElCard, ElMessage, ElMessageBox } from 'element-plus';

const props = withDefaults(
  defineProps<{
    options: any;
    visible: boolean;
  }>(),
  {
    visible: true,
    options: {
      defaultUnit: null,
      editable: true,
      data: [],
    },
  },
);

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'insert'): void; // 新增行
  (e: 'addUnit', row?: any): void; // 新增单位
  (e: 'editUnit', id: string, row: any): void; // 编辑单位
  (e: 'delUnit', id: string): void; // 删除单位
  (e: 'refresh'): void; // 刷新
}>();

const drawerRef = ref<HTMLElement | null>(null);
const drawerHeight = ref(318);

const drawerVisible = ref(props.visible); // 是否展示
const isOpen = ref(false); // 默认是否开启状态
const _options = ref(props.options); // 配置类型
watch(
  () => props.options,
  (nval) => {
    _options.value = nval;
  },
);

// 获取dom高度
const updateDrawerHeight = () => {
  nextTick(() => {
    if (drawerRef.value) {
      drawerHeight.value = drawerRef.value.offsetHeight;
    }
  });
};

watch(isOpen, (val) => {
  if (!val) updateDrawerHeight();
});

const currentRow = ref<any>(); // 当前选中数据
const tableRef = ref();
const tableOptions = reactive<any>({
  border: true,
  size: 'mini',
  align: 'center',
  height: '208',
  showOverflow: true,
  resizable: true,
  rowClassName: ({ row }: any) => {
    return row.isOperation ? '' : 'bg-gray-100';
  },
  headerCellConfig: {
    height: 40,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  keepSource: true,
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      const isActive = row.isOperation;
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DELETE_ROW': {
            item.disabled = !isActive;
            break;
          }
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (!row.isOperation) {
        return false;
      }
      return true;
    },
  },
  editRules: {
    unit: [{ required: true, message: '换算单位不得为空！' }],
    factor: [{ required: true, message: '换算系数不得为空！' }],
    factorExplain: [{ required: true, message: '系数说明不得为空！' }],
  },
  columns: [
    {
      type: 'seq',
      fixed: 'left',
      width: 80,
    },
    {
      field: 'unit',
      title: '换算单位',
      editRender: {},
      slots: {
        edit: 'unit',
      },
    },
    {
      field: 'factor',
      title: '换算系数',
      editRender: {},
      slots: {
        edit: 'factor',
      },
    },
    {
      field: 'factorExplain',
      title: '系数说明',
      formatter: ({ row }: any) => {
        return row.unit && row.unit !== ''
          ? `1*${row.unit} = ${row.factor}*${_options.value.defaultUnit}`
          : '';
      },
    },
    {
      field: 'type',
      title: '设置类型',
      slots: {
        default: 'type',
      },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
    },
  ],
  data: [],
});
watch(
  () => _options.value.data,
  (nval) => {
    tableOptions.data = nval;
  },
  {
    immediate: true,
    deep: true,
  },
);
const tableEvents = {
  async cellClick({ row }: any) {
    currentRow.value = row;
  },
  async editClosed({ row }: any) {
    if (row.id) {
      emit('editUnit', row.id, row);
    } else {
      emit('addUnit', row);
    }
  },
  async menuClick({ menu }: any) {
    switch (menu.code) {
      case 'DELETE_ROW': {
        ElMessageBox.confirm('确定删除该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          if (currentRow.value.id) {
            emit('delUnit', currentRow.value.id);
          } else {
            const row = tableOptions.data.find(
              (item: any) => item.id === currentRow.value.id,
            );
            tableRef.value.remove(row);
          }
        });
        break;
      }
    }
  },
};
// 新增单位
async function addUnit() {
  emit('insert');
}
// 新增单位换算
async function addRow(row: any) {
  await nextTick(() => {
    tableRef.value && tableRef.value.insertAt(row);
    tableRef.value.validate(true);
  });
}
// 关闭按钮点击
function closeClick() {
  isOpen.value = false;
  emit('update:visible', false);
}
// 改变展示装
function changeOpenStatus() {
  if (_options.value.defaultUnit === null) {
    ElMessage.warning('请先选择源数据');
    return;
  }
  isOpen.value = !isOpen.value;
}

// 暴露初始化方法
defineExpose({
  addRow,
});
</script>

<style lang="scss">
.el-card__header {
  padding: 10px 16px;
}

.unitconversion {
  position: absolute;
  bottom: 0;
}
</style>
